<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>SYK School - Register</title>
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&amp;display=swap" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
      --primary-color: #1173d4;
      --secondary-color: #e0effc;
      --background-color: #f8faff;
      --text-primary: #1a202c;
      --text-secondary: #5c6b7d;
      --accent-color: #1173d4;
    }
    body {
      font-family: 'Inter', sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
    }
    .form-input {
      background-color: #ffffff;
      border: 1px solid #e5e7eb;
    }
  </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)]">
    <div class="flex flex-col min-h-screen justify-center">
        <header class="absolute top-0 left-0 right-0 bg-white shadow-sm">
            <div class="container mx-auto px-4 py-4 max-w-md">
                <div class="flex items-center justify-between">
                    <button class="text-[var(--text-primary)]">
                        <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                            </path>
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold text-[var(--text-primary)]">Register</h1>
                    <div class="w-6"></div>
                </div>
            </div>
        </header>
        <main class="flex-grow flex items-center">
            <div class="container mx-auto px-4 py-8 max-w-md">
                <div class="bg-white rounded-xl shadow-md p-6 sm:p-8">
                    <form action="#" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-[var(--text-primary)] mb-2" for="email">Email
                                Address</label>
                            <input autocomplete="email"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent text-[var(--text-primary)] placeholder-[var(--text-secondary)]"
                                id="email" name="email" placeholder="<EMAIL>" required="" type="email" />
                        </div>
                        <div>
                            <div class="flex justify-between items-center mb-2">
                                <label class="block text-sm font-medium text-[var(--text-primary)]"
                                    for="password">Password</label>
                            </div>
                            <input autocomplete="new-password"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent text-[var(--text-primary)] placeholder-[var(--text-secondary)]"
                                id="password" name="password" placeholder="••••••••" required="" type="password" />
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-[var(--text-primary)] mb-2"
                                for="confirm-password">Confirm Password</label>
                            <input autocomplete="new-password"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent text-[var(--text-primary)] placeholder-[var(--text-secondary)]"
                                id="confirm-password" name="confirm-password" placeholder="••••••••" required=""
                                type="password" />
                        </div>
                        <div class="text-right">
                            <a class="text-sm font-medium text-[var(--primary-color)] hover:underline" href="#">Forgot
                                Password?</a>
                        </div>
                        <div>
                            <button
                                class="w-full bg-[var(--primary-color)] text-white py-3 px-6 rounded-lg font-semibold hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition-colors"
                                type="submit">
                                Register
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
        <footer class="pb-6 pt-2">
            <p class="text-center text-sm text-[var(--text-secondary)]">
                Already have an account?
                <a class="font-medium text-[var(--primary-color)] hover:underline" href="#">Sign in</a>
            </p>
        </footer>
    </div>

</body>

</html>