<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style" href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;700;900"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #d4e7f9;
        --background-color: #f8faff;
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --accent-color: #82b9f0;
        --border-radius-sm: 0.125rem;
        --border-radius-md: 0.375rem;
        --border-radius-lg: 0.5rem;
        --border-radius-xl: 0.75rem;
        --border-radius-2xl: 1rem;
        --border-radius-3xl: 1.5rem;
      }
      body {
        font-family: 'Inter', sans-serif;
      }
    </style>
    <title>Stitch Design</title>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)]">
    <div
        class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden text-[var(--text-primary)]">
        <div class="flex flex-col gap-4">
            <div class="flex items-center bg-white p-4 pb-2 justify-between shadow-sm">
                <div class="text-[var(--text-primary)] flex size-10 shrink-0 items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
                    data-icon="ArrowLeft" data-size="24px" data-weight="regular">
                    <svg fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                        </path>
                    </svg>
                </div>
                <h2 class="text-lg font-bold leading-tight tracking-tight flex-1 text-center pr-10">Reset Password</h2>
            </div>
            <div class="px-4 py-2">
                <p class="text-[var(--text-secondary)] text-base font-normal leading-relaxed">
                    Create a new, strong password. Your password must be at least 8 characters long.
                </p>
            </div>
            <div class="flex flex-col gap-6 px-4">
                <div class="flex flex-col gap-2">
                    <label class="text-sm font-medium text-[var(--text-secondary)]" for="new-password">New
                        Password</label>
                    <div class="relative">
                        <input
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl border border-gray-300 bg-white px-4 py-3.5 text-base text-[var(--text-primary)] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50"
                            id="new-password" placeholder="Enter new password" type="password" />
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400">
                            <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M247.31,124.76c-.35-.79-8.42-18.58-27.65-37.8C199.27,66.58,165,48,128,48S56.73,66.58,36.34,86.96C17.11,106.18,9,124,8.69,124.76a8,8,0,0,0,0,6.48c.35.79,8.42,18.58,27.65,37.8C56.73,189.42,91,208,128,208s71.27-18.58,91.66-38.96c19.23-19.22,27.3-37,27.65-37.8A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-59.93-15.67-79.33-33.58C62.81,143.79,75.31,132.48,87,125.15a64,64,0,1,1,82,0c11.72,7.33,24.22,18.64,38.37,33.27C187.93,176.33,158.78,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="flex flex-col gap-2">
                    <label class="text-sm font-medium text-[var(--text-secondary)]" for="confirm-password">Confirm New
                        Password</label>
                    <div class="relative">
                        <input
                            class="form-input flex w-full min-w-0 flex-1 resize-none overflow-hidden rounded-xl border border-gray-300 bg-white px-4 py-3.5 text-base text-[var(--text-primary)] placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50"
                            id="confirm-password" placeholder="Confirm new password" type="password" />
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400">
                            <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M247.31,124.76c-.35-.79-8.42-18.58-27.65-37.8C199.27,66.58,165,48,128,48S56.73,66.58,36.34,86.96C17.11,106.18,9,124,8.69,124.76a8,8,0,0,0,0,6.48c.35.79,8.42,18.58,27.65,37.8C56.73,189.42,91,208,128,208s71.27-18.58,91.66-38.96c19.23-19.22,27.3-37,27.65-37.8A8,8,0,0,0,247.31,124.76ZM128,192c-30.78,0-59.93-15.67-79.33-33.58C62.81,143.79,75.31,132.48,87,125.15a64,64,0,1,1,82,0c11.72,7.33,24.22,18.64,38.37,33.27C187.93,176.33,158.78,192,128,192Zm0-112a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-4 pb-6">
            <button
                class="flex w-full cursor-pointer items-center justify-center overflow-hidden rounded-xl h-14 px-5 bg-[var(--primary-color)] text-white text-base font-bold leading-normal tracking-wide shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition ease-in-out duration-150">
                <span class="truncate">Reset Password</span>
            </button>
        </div>
    </div>

</body>

</html>