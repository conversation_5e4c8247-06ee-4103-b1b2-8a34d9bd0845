<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;700;900&amp;family=Noto+Sans:wght@400;500;700;900"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <title>SYK School App - Login</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
      --primary-color: #1173d4;
      --secondary-color: #e0effc;
      --background-color: #f8faff;
      --text-primary: #1a202c;
      --text-secondary: #5a677d;
      --accent-color: #1173d4;
    }
    body {
      font-family: 'Inter', sans-serif;
      background-color: var(--background-color);
    }
  </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="text-[var(--text-primary)]">
    <div
        class="relative flex size-full min-h-screen flex-col items-center justify-center bg-[var(--background-color)] p-6 group/design-root">
        <div class="flex w-full max-w-sm flex-col">
            <header class="absolute top-4 left-4">
                <button class="text-[var(--text-primary)]">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                        </path>
                    </svg>
                </button>
            </header>
            <main class="flex flex-col">
                <div class="text-center mb-10">
                    <h1 class="text-3xl font-bold text-[var(--text-primary)] mb-2">Welcome Back!</h1>
                    <p class="text-[var(--text-secondary)]">Log in to your SYK School account.</p>
                </div>
                <form class="space-y-6">
                    <div>
                        <label class="sr-only" for="email">Email</label>
                        <input
                            class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent transition-all duration-200 ease-in-out text-[var(--text-primary)] placeholder-text-secondary bg-white"
                            id="email" placeholder="Email" type="email" />
                    </div>
                    <div>
                        <label class="sr-only" for="password">Password</label>
                        <input
                            class="w-full p-4 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent transition-all duration-200 ease-in-out text-[var(--text-primary)] placeholder-text-secondary bg-white"
                            id="password" placeholder="Password" type="password" />
                    </div>
                    <div class="text-right">
                        <a class="text-sm font-medium text-[var(--primary-color)] hover:underline" href="#">Forgot
                            Password?</a>
                    </div>
                    <button
                        class="bg-[var(--primary-color)] text-white py-4 px-6 rounded-xl hover:bg-[var(--accent-color)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition-colors duration-200 ease-in-out w-full font-bold text-lg"
                        type="submit">
                        Login
                    </button>
                </form>
            </main>
        </div>
    </div>

</body>

</html>