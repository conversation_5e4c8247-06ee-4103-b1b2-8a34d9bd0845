<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <title>SYK School - Campus Map</title>
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;600;700&amp;family=Plus+Jakarta+Sans:wght@400;500;700;800"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
      --primary-color: #1173d4;
      --secondary-color: #e0effc;
      --background-color: #f8faff;
      --text-primary: #1a202c;
      --text-secondary: #5a677d;
      --accent-color: #1173d4;
    }
    body {
      font-family: 'Inter', 'Plus Jakarta Sans', sans-serif;
      background-color: var(--background-color);
      color: var(--text-primary);
    }
    .map-container {
      background-image: url("https://lh3.googleusercontent.com/aida-public/AB6AXuCoL051vuCAQND1eq05ayxw4mjSlT5PRx5SH76GeGgsPCSSjdT-uV2UR_RjFl5V4wGmA0inMbRJzFlrLCRCmIjLGrg0xaTx4mD-FpNZdOyq3iG-57DyfTbPEbVDksfIjkNxoTGHyvjHDo1ZHH_4QBxq2WxHKvOxGQpjDKqe78qwUCAX3ObRw8YUhFEmApsCH7vhCk3xadxRmNJiEQv8zVCgwP8kdgO8Or6JeBYN370Z7hLB1GhZuDPS6uYjGIjWjUacOlaTyJKFFmKS");
      height: 100vh;
    }
    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 9999px;
      border: 2px solid white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    .user-marker {
      width: 20px;
      height: 20px;
      border-radius: 9999px;
      background-color: var(--primary-color);
      border: 3px solid white;
      box-shadow: 0 0 0 2px var(--primary-color);
    }
    .slide-up-panel {
        transform: translateY(calc(100% - 80px));
        transition: transform 0.3s ease-in-out;
    }
    .slide-up-panel.open {
        transform: translateY(0);
    }
  </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
    <div class="relative flex size-full min-h-screen flex-col">
        <main class="flex flex-1 flex-col">
            <div class="relative flex flex-1 flex-col">
                <div class="map-container relative w-full bg-cover bg-center">
                    <div class="absolute top-1/3 left-1/4">
                        <img alt="Friend Avatar" class="avatar"
                            src="https://lh3.googleusercontent.com/aida-public/AB6AXuBLrbrLgxUCaVc5c29_94atTnlzfZuHa02isOaMVg_YIbOAqqaXEfECh95zj-2JtfQ5z-LXRSSaeI1-8GDHbJ79Sp1uPt2yMzlcHTio0rgU4SFnZ_PQ2CRNDL9BJw3LxT3_ZbmlUVrMIfm8xfrxBGAOUmVreazWykD0g7ae-3CMWclS2KysPqlzBU_9V2CAv_aYZT0oHf-rEAAAEfi9ruu2s-UetRVhaCcHnONB73KuKJRZW_6to0UXNx33HjbHwh5uOh4WUGe3Qa0Q" />
                    </div>
                    <div class="absolute top-1/2 left-2/3">
                        <img alt="Friend Avatar" class="avatar"
                            src="https://lh3.googleusercontent.com/aida-public/AB6AXuCa3gy7Ih5Y3y1OXBDSgLCGNfcq2Yg-HHP7o-c-EcOLjxDR_I7MoMl1a6HZpt9kxK1xleAIoTh3Gw4Pno_4FDhGgIUESe46Nba4W8iDqeTaP92JG1G_ScidNcyYS0KiHmuHebN7kF-jYT6zZ-9urT8Lz26uIkGOWACe6gEkI9WPgovqrAqvV9BQBxRVPlVw54HZ9loGPfQw8BBA1Ad2O2uvoiUg0_zB0LpQ6aCnKK-J_n-GzHyIZCn7brvpVu8LftLjVsKDXVtovvwo" />
                    </div>
                    <div class="absolute bottom-1/4 right-1/4">
                        <div class="user-marker"></div>
                    </div>
                </div>
                <div class="absolute top-4 left-4 right-4 z-10">
                    <div
                        class="search_bar flex items-center bg-white rounded-full shadow-md p-2 border border-gray-200">
                        <svg class="text-gray-400 mx-2" fill="currentColor" height="20" viewBox="0 0 256 256" width="20"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                            </path>
                        </svg>
                        <input
                            class="search_input flex-grow bg-transparent p-1 text-[var(--text-primary)] placeholder-[var(--text-secondary)] focus:outline-none"
                            placeholder="Search rooms or friends..." type="text" />
                    </div>
                </div>
                <div class="absolute bottom-[280px] right-4 z-10 flex flex-col items-center gap-3">
                    <div class="flex flex-col rounded-lg bg-white shadow-md">
                        <button
                            class="icon_button p-3 hover:bg-gray-100 transition-colors duration-200 ease-in-out rounded-t-lg">
                            <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M224,128a8,8,0,0,1-8,8H136v80a8,8,0,0,1-16,0V136H40a8,8,0,0,1,0-16h80V40a8,8,0,0,1,16,0v80h80A8,8,0,0,1,224,128Z">
                                </path>
                            </svg>
                        </button>
                        <hr class="border-gray-200" />
                        <button
                            class="icon_button p-3 hover:bg-gray-100 transition-colors duration-200 ease-in-out rounded-b-lg">
                            <svg fill="currentColor" height="20" viewBox="0 0 256 256" width="20"
                                xmlns="http://www.w3.org/2000/svg">
                                <path d="M224,128a8,8,0,0,1-8,8H40a8,8,0,0,1,0-16H216A8,8,0,0,1,224,128Z"></path>
                            </svg>
                        </button>
                    </div>
                    <button
                        class="icon_button flex size-12 items-center justify-center rounded-full bg-white shadow-md">
                        <svg class="text-[var(--primary-color)]" fill="currentColor" height="24" viewBox="0 0 256 256"
                            width="24" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M229.33,98.21,53.41,33l-.16-.05A16,16,0,0,0,32.9,53.25a1,1,0,0,0,.05.16L98.21,229.33A15.77,15.77,0,0,0,113.28,240h.3a15.77,15.77,0,0,0,15-11.29l23.56-76.56,76.56-23.56a16,16,0,0,0,.62-30.38ZM224,113.3l-76.56,23.56a16,16,0,0,0-10.58,10.58L113.3,224h0l-.06-.17L48,48l175.82,65.22.16.06Z">
                            </path>
                        </svg>
                    </button>
                </div>
            </div>
        </main>
        <div class="fixed bottom-16 left-0 right-0 z-20">
            <div class="slide-up-panel bg-white rounded-t-2xl shadow-[0_-4px_12px_rgba(0,0,0,0.08)] mx-2 pt-2">
                <div class="flex justify-center mb-2">
                    <button class="w-10 h-1.5 bg-gray-300 rounded-full"
                        onclick="this.parentElement.parentElement.classList.toggle('open')"></button>
                </div>
                <div class="bg-white">
                    <div class="flex border-b border-gray-200 px-4">
                        <a class="flex flex-1 flex-col items-center justify-center border-b-2 border-[var(--primary-color)] py-3 text-[var(--primary-color)]"
                            href="#">
                            <p class="text-sm font-semibold">Floor 1</p>
                        </a>
                        <a class="flex flex-1 flex-col items-center justify-center border-b-2 border-transparent py-3 text-[var(--text-secondary)] hover:border-gray-300 hover:text-[var(--text-primary)] transition-colors duration-200"
                            href="#">
                            <p class="text-sm font-semibold">Floor 2</p>
                        </a>
                        <a class="flex flex-1 flex-col items-center justify-center border-b-2 border-transparent py-3 text-[var(--text-secondary)] hover:border-gray-300 hover:text-[var(--text-primary)] transition-colors duration-200"
                            href="#">
                            <p class="text-sm font-semibold">Floor 3</p>
                        </a>
                    </div>
                </div>
                <div class="p-4 overflow-y-auto" style="max-height: calc(100vh - 300px);">
                    <h3 class="font-bold text-lg mb-3">Nearby Friends</h3>
                    <div class="space-y-3">
                        <div class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 cursor-pointer">
                            <img alt="Friend Avatar" class="avatar"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuBLrbrLgxUCaVc5c29_94atTnlzfZuHa02isOaMVg_YIbOAqqaXEfECh95zj-2JtfQ5z-LXRSSaeI1-8GDHbJ79Sp1uPt2yMzlcHTio0rgU4SFnZ_PQ2CRNDL9BJw3LxT3_ZbmlUVrMIfm8xfrxBGAOUmVreazWykD0g7ae-3CMWclS2KysPqlzBU_9V2CAv_aYZT0oHf-rEAAAEfi9ruu2s-UetRVhaCcHnONB73KuKJRZW_6to0UXNx33HjbHwh5uOh4WUGe3Qa0Q" />
                            <div>
                                <p class="font-semibold text-sm">Olivia Chen</p>
                                <p class="text-xs text-gray-500">Library, 2nd Floor</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-100 cursor-pointer">
                            <img alt="Friend Avatar" class="avatar"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuCa3gy7Ih5Y3y1OXBDSgLCGNfcq2Yg-HHP7o-c-EcOLjxDR_I7MoMl1a6HZpt9kxK1xleAIoTh3Gw4Pno_4FDhGgIUESe46Nba4W8iDqeTaP92JG1G_ScidNcyYS0KiHmuHebN7kF-jYT6zZ-9urT8Lz26uIkGOWACe6gEkI9WPgovqrAqvV9BQBxRVPlVw54HZ9loGPfQw8BBA1Ad2O2uvoiUg0_zB0LpQ6aCnKK-J_n-GzHyIZCn7brvpVu8LftLjVsKDXVtovvwo" />
                            <div>
                                <p class="font-semibold text-sm">Ethan Rodriguez</p>
                                <p class="text-xs text-gray-500">Cafeteria</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <nav class="fixed bottom-0 left-0 right-0 z-30 bg-white border-t border-gray-200">
            <div class="flex h-16">
                <a class="flex flex-1 flex-col items-center justify-center gap-1 text-[var(--primary-color)]" href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M128,64a40,40,0,1,0,40,40A40,40,0,0,0,128,64Zm0,64a24,24,0,1,1,24-24A24,24,0,0,1,128,128Zm0-112a88.1,88.1,0,0,0-88,88c0,34.45,21.07,73.1,59.39,105.21a8,8,0,0,0,11.19.16C152.09,212.18,175,176.62,175,144.33V104a72.08,72.08,0,0,0-72-72H96A88.1,88.1,0,0,0,8,104c0,34.45,21.07,73.1,59.39,105.21a8,8,0,0,0,11.19.16c4.2-3.56,8.23-7.3,12-11.14a8,8,0,0,0-11.19-11.42c-3.5,3.58-7.25,7-11.23,10.15C47,170.83,32,137.28,32,104a64,64,0,0,1,64-64h6.67A56.06,56.06,0,0,1,159,103.67V144.33c0,33.51,19.82,69.43,54.55,98.67a8,8,0,0,0,11.2-.18C228.3,219,240,186.2,240,152a88.1,88.1,0,0,0-88-88Z">
                        </path>
                    </svg>
                    <span class="text-xs font-medium">Map</span>
                </a>
                <a class="flex flex-1 flex-col items-center justify-center gap-1 text-[var(--text-secondary)] hover:text-[var(--primary-color)]"
                    href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M172,56a4,4,0,0,1,4,4V80a4,4,0,0,1-8,0V60A4,4,0,0,1,172,56Zm-88,28a44,44,0,1,0,44-44A44.05,44.05,0,0,0,84,40Zm0,72a28,28,0,0,0,0,56,4,4,0,0,0,0-8,20,20,0,1,1,20-20,4,4,0,0,0,8,0,28,28,0,0,0-28-28Zm88-44a44,44,0,1,0,44,44A44.05,44.05,0,0,0,172,40Zm0,72a28,28,0,1,0,28,28A28,28,0,0,0,172,112Zm-88,72a44,44,0,1,0,44,44A44.05,44.05,0,0,0,84,140Zm22.46,65.41A44,44,0,0,0,172,228a4,4,0,0,0,0-8,36,36,0,0,1-65.09-17.41,4,4,0,1,0-7.82,1.82Z">
                        </path>
                    </svg>
                    <span class="text-xs font-medium">Friends</span>
                </a>
                <a class="flex flex-1 flex-col items-center justify-center gap-1 text-[var(--text-secondary)] hover:text-[var(--primary-color)]"
                    href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M234.33,133.56a8,8,0,0,0-10.66-2.l-34.46,25.85a64,64,0,0,0-62.42,0L92.33,131.56a8,8,0,0,0-10.66,12.88l34.46,25.85a63.53,63.53,0,0,0,0,15.42l-34.46,25.85a8,8,0,0,0,10.66,12.88l34.46-25.85a64,64,0,0,0,62.42,0l34.46,25.85a8,8,0,0,0,10.66-12.88l-34.46-25.85a63.53,63.53,0,0,0,0-15.42l34.46-25.85A8,8,0,0,0,234.33,133.56ZM128,192a48,48,0,1,1,48-48A48.05,48.05,0,0,1,128,192Zm99.48-73.34-22,16.5a8,8,0,0,0-1.89,11.66,80,80,0,1,1-103.18,0,8,8,0,0,0-1.89-11.66l-22-16.5a8,8,0,1,0-9.08,12.68l22,16.5a8,8,0,0,0,1.89,1.61,96,96,0,1,0,121.34,0,8,8,0,0,0,1.89-1.61l22-16.5a8,8,0,1,0-9.08-12.68Z">
                        </path>
                    </svg>
                    <span class="text-xs font-medium">Settings</span>
                </a>
            </div>
        </nav>
    </div>

</body>

</html>