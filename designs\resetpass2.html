<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Inter:wght@400;500;700;900&amp;family=Noto+Sans:wght@400;500;700;900"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #e0effc;
        --background-color: #f8faff;
        --text-primary: #1a202c;
        --text-secondary: #5a677d;
        --accent-color: #1173d4;
      }
      body {
        font-family: 'Inter', sans-serif;
      }
    </style>
    <title>SYK School - Password Reset</title>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
    <div class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden">
        <header class="p-4">
            <div class="flex items-center justify-between">
                <button class="flex items-center justify-center size-10 text-[var(--text-primary)]">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                        </path>
                    </svg>
                </button>
                <h1 class="text-lg font-bold text-[var(--text-primary)] flex-1 text-center pr-10">Reset Password</h1>
            </div>
        </header>
        <main class="flex flex-col items-center justify-center text-center px-6 flex-grow">
            <div class="mb-8">
                <svg fill="none" height="80" viewBox="0 0 80 80" width="80" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="40" cy="40" fill="#E0EFFC" r="40"></circle>
                    <path clip-rule="evenodd"
                        d="M26 36.667c0-1.84 0-2.76.34-3.5C26.68 32.487 27.18 32 28 31.66c.82-.34 1.74-.34 3.58-.34h16.84c1.84 0 2.76 0 *********.34 1.32.847 1.66 **********.34 1.6.34 3.48v1.36c0 1.94 0 2.91-.35 3.66-.35.75-.89 1.29-1.63 1.63-.75.35-1.71.35-3.66.35H50c1.95 0 2.93 0 **********.34 1.29.88 1.63 **********.35 1.71.35 3.66v1.34c0 1.88 0 2.82-.34 3.5-.34.673-.84 1.18-1.52 1.52-.68.34-1.6.34-3.48.34H31.58c-1.84 0-2.76 0-3.5-.34-.82-.34-1.32-.847-1.66-1.52-.34-.68-.34-1.6-.34-3.48v-1.36c0-1.94 0-2.91.35-3.66.35-.75.89-1.29 1.63-1.63.75-.35 1.71.35 3.66.35h-2.22c-1.95 0-2.93 0-3.68-.35-.75-.34-1.29-.88-1.63-1.63-.35-.75-.35-1.71-.35-3.66v-1.34Z"
                        stroke="#1173D4" stroke-linecap="round" stroke-linejoin="round" stroke-width="4"></path>
                </svg>
            </div>
            <h2 class="text-2xl font-bold text-[var(--text-primary)] mb-3">Check your email</h2>
            <p class="text-base text-[var(--text-secondary)] leading-relaxed max-w-sm">
                We've sent a password reset link to your email address. Please check your inbox and follow the
                instructions to reset your password.
            </p>
        </main>
        <footer class="p-4 pb-8">
            <button
                class="w-full h-12 px-5 rounded-xl bg-[var(--primary-color)] text-white text-base font-bold tracking-wide focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition-colors duration-200">
                <span class="truncate">Back to Login</span>
            </button>
            <p class="text-center text-sm text-[var(--text-secondary)] mt-4">
                Didn't receive the email? <a class="font-medium text-[var(--primary-color)] hover:underline"
                    href="#">Resend link</a>
            </p>
        </footer>
    </div>

</body>

</html>