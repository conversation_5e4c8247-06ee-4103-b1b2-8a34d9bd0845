<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>SYK School App - Welcome</title>
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&amp;display=swap"
        rel="stylesheet" />
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #e0effc;
        --background-color: #F7F8FC;
        --text-primary: #0F172A;
        --text-secondary: #64748B;
        --accent-color: #38BDF8;
      }
      body {
        font-family: "Inter", sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
      }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)]">
    <div class="flex flex-col h-screen justify-between max-w-md mx-auto bg-white p-8">
        <div class="flex-grow flex flex-col justify-center">
            <div class="text-center mb-12">
                <h1 class="text-4xl font-extrabold text-[var(--text-primary)] mb-3">
                    Welcome to SYK
                </h1>
                <p class="text-[var(--text-secondary)] text-lg leading-relaxed">
                    Find your friends on campus in real-time.
                </p>
            </div>
            <div class="space-y-4">
                <button
                    class="w-full bg-[var(--primary-color)] text-white py-4 px-6 rounded-xl font-bold text-lg hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition-all duration-300 ease-in-out transform hover:-translate-y-1">
                    Register with Email
                </button>
                <div class="relative flex items-center">
                    <div class="flex-grow border-t border-gray-200"></div>
                    <span class="flex-shrink mx-4 text-sm font-medium text-gray-400">OR</span>
                    <div class="flex-grow border-t border-gray-200"></div>
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <button
                        class="w-full bg-white border border-gray-200 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 transition-colors duration-200 ease-in-out flex items-center justify-center">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_105_239)">
                                <path
                                    d="M22.56 12.25C22.56 11.45 22.49 10.68 22.36 9.92H12V14.28H18.19C17.93 15.63 17.15 16.79 15.98 17.58V20.18H19.89C21.68 18.57 22.56 15.69 22.56 12.25Z"
                                    fill="#4285F4"></path>
                                <path
                                    d="M12 23C14.97 23 17.45 22.02 19.28 20.18L15.98 17.58C14.91 18.25 13.58 18.66 12 18.66C9.21 18.66 6.83 16.88 5.92 14.38H1.92V16.98C3.72 20.65 7.55 23 12 23Z"
                                    fill="#34A853"></path>
                                <path
                                    d="M5.92 14.38C5.71 13.78 5.58 13.14 5.58 12.5C5.58 11.86 5.71 11.22 5.92 10.62V8.02H1.92C1.18 9.47 0.75 10.94 0.75 12.5C0.75 14.06 1.18 15.53 1.92 16.98L5.92 14.38Z"
                                    fill="#FBBC05"></path>
                                <path
                                    d="M12 6.34C13.63 6.34 15.05 6.94 16.14 7.97L19.34 4.77C17.45 3.06 14.97 2 12 2C7.55 2 3.72 4.35 1.92 8.02L5.92 10.62C6.83 8.12 9.21 6.34 12 6.34Z"
                                    fill="#EA4335"></path>
                            </g>
                            <defs>
                                <clipPath id="clip0_105_239">
                                    <rect fill="white" height="24" width="24"></rect>
                                </clipPath>
                            </defs>
                        </svg>
                    </button>
                    <button
                        class="w-full bg-white border border-gray-200 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 transition-colors duration-200 ease-in-out flex items-center justify-center">
                        <svg class="w-6 h-6" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M12.01 6.36c-.1 0-.19.01-.28.01l.25-.26c.32-.33.56-.73.69-1.18l.1-.33c.03-.1-.04-.2-.14-.2s-.17.06-.2.15l-.11.37c-.11.38-.3.7-.54.94l-.25.26c-.4.29-.85.47-1.33.52l-.4.04h-.43c-.63 0-1.23-.2-1.73-.55l-.31-.22c-.22-.15-.5-.15-.72.01l-.22.16c-.2.15-.3.39-.24.62l.13.48c.18.68.59 1.28 1.14 1.68l.32.23c.48.34 1.05.54 1.66.54h.4l.43-.03c.52-.06 1-.26 1.41-.56l.24-.17c.23-.17.54-.12.71.11l.21.28c.***********.6.11l.4-.23c.21-.12.28-.4.16-.61l-.2-.34a1.9 1.9 0 0 0-1.1-1.03l-.3-.12c-.17-.07-.35-.1-.53-.1m-1.24 11.23c.26.3.43.68.51 1.08l.06.33c.02.1-.05.2-.15.2s-.18-.07-.2-.16l-.07-.38c-.06-.34-.2-.65-.4-.89l-.26-.3c-.39-.45-.94-.74-1.55-.8l-.48-.05h-.37c-.68 0-1.32.22-1.85.6l-.32.23c-.22.15-.5.15-.72.01l-.22-.16c-.2-.15-.3-.39-.24-.62l.13-.48c.18-.68.59-1.28 1.14-1.68l.32-.23c.53-.38 1.17-.6 1.83-.6h.38l.5.05c.57.06 1.1.28 1.54.63l.26.21c.23.18.53.15.7-.08l.25-.34c.14-.2.4-.23.6-.08l.36.21c.21.12.28.4.16-.61l-.12-.21c-.24-.43-.58-.79-1-.99l-.32-.15c-.2-.09-.4-.14-.6-.14c-.04 0-.08 0-.12.01l-.19.03c-1.3.17-2.38.9-2.98 2.11a3.48 3.48 0 0 0-.02 3.07c.6 1.2 1.68 1.94 2.97 2.1l.2.02c.04 0 .08.01.12.01c.2 0 .4-.05.59-.14l.32-.15c.42-.2.76-.56 1-1l.12-.21c.12-.21.05-.49-.16-.61l-.36-.21c-.2-.11-.46-.08-.6.08l-.25.34c-.17.23-.47.26-.7.08l-.26-.22Z"
                                fill="#000000"></path>
                        </svg>
                    </button>
                    <button
                        class="w-full bg-white border border-gray-200 text-gray-700 py-3 px-4 rounded-xl font-semibold hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-300 focus:ring-opacity-50 transition-colors duration-200 ease-in-out flex items-center justify-center">
                        <svg class="w-6 h-6" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M2.5 12H11.5V21H2.5V12Z" fill="#F35325"></path>
                            <path d="M12.5 12H21.5V21H12.5V12Z" fill="#81BC06"></path>
                            <path d="M2.5 3H11.5V12H2.5V3Z" fill="#05A6F0"></path>
                            <path d="M12.5 3H21.5V12H12.5V3Z" fill="#FFBA08"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        <div class="pb-2">
            <p class="text-center text-base text-[var(--text-secondary)]">
                Already have an account?
                <a class="font-semibold text-[var(--primary-color)] hover:underline" href="#">Log in</a>
            </p>
        </div>
    </div>

</body>

</html>