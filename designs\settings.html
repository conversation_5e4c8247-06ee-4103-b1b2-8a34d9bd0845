<html>

<head>
    <meta charset="utf-8" />
    <link crossorigin="" href="https://fonts.gstatic.com/" rel="preconnect" />
    <link as="style"
        href="https://fonts.googleapis.com/css2?display=swap&amp;family=Noto+Sans%3Awght%40400%3B500%3B700%3B900&amp;family=Plus+Jakarta+Sans%3Awght%40400%3B500%3B700%3B800"
        onload="this.rel='stylesheet'" rel="stylesheet" />
    <title>Stitch Design</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #d4e7f9;
        --background-color: #f8faff;
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --accent-color: #1173d4;
      }
      body {
        font-family: 'Plus Jakarta Sans', sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
      }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
    <div class="relative flex size-full min-h-screen flex-col justify-between group/design-root"
        style='font-family: "Plus Jakarta Sans", "Noto Sans", sans-serif;'>
        <div class="flex-grow">
            <header class="bg-white sticky top-0 z-10 shadow-sm">
                <div class="mx-auto max-w-md">
                    <div class="flex items-center p-4">
                        <button class="p-2 -ml-2">
                            <svg class="text-[var(--text-primary)]" fill="currentColor" height="24px"
                                viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                                </path>
                            </svg>
                        </button>
                        <h1 class="text-lg font-bold flex-1 text-center">Settings</h1>
                        <div class="w-8"></div>
                    </div>
                </div>
            </header>
            <main class="py-6 px-4 max-w-md mx-auto">
                <section class="space-y-4">
                    <h2 class="text-[22px] font-bold text-[var(--text-primary)]">Privacy</h2>
                    <div class="bg-white rounded-xl shadow-sm">
                        <div class="flex items-center justify-between p-4 border-b border-gray-100">
                            <div class="flex-1">
                                <p class="text-base font-medium text-[var(--text-primary)]">Share my location</p>
                                <p class="text-sm text-[var(--text-secondary)]">Your friends can see your location on
                                    the map</p>
                            </div>
                            <label
                                class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full border-none bg-gray-200 p-0.5 has-[:checked]:justify-end has-[:checked]:bg-[var(--primary-color)]">
                                <div class="h-full w-[27px] rounded-full bg-white shadow-sm"></div>
                                <input checked="" class="invisible absolute" type="checkbox" />
                            </label>
                        </div>
                        <div class="flex items-center justify-between p-4">
                            <div class="flex-1">
                                <p class="text-base font-medium text-[var(--text-primary)]">Invisible mode</p>
                                <p class="text-sm text-[var(--text-secondary)]">Your friends won't be able to see your
                                    location</p>
                            </div>
                            <label
                                class="relative flex h-[31px] w-[51px] cursor-pointer items-center rounded-full border-none bg-gray-200 p-0.5 has-[:checked]:justify-end has-[:checked]:bg-[var(--primary-color)]">
                                <div class="h-full w-[27px] rounded-full bg-white shadow-sm"></div>
                                <input class="invisible absolute" type="checkbox" />
                            </label>
                        </div>
                    </div>
                    <a class="flex items-center justify-between bg-white rounded-xl shadow-sm p-4" href="#">
                        <p class="text-base font-medium text-[var(--text-primary)]">Manage Blocked Friends</p>
                        <svg class="text-gray-400" fill="currentColor" height="20px" viewBox="0 0 256 256" width="20px"
                            xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                            </path>
                        </svg>
                    </a>
                </section>
                <section class="space-y-4 mt-8">
                    <h2 class="text-[22px] font-bold text-[var(--text-primary)]">General</h2>
                    <div class="bg-white rounded-xl shadow-sm">
                        <a class="flex items-center justify-between p-4 border-b border-gray-100" href="#">
                            <p class="text-base font-medium text-[var(--text-primary)]">Notifications</p>
                            <svg class="text-gray-400" fill="currentColor" height="20px" viewBox="0 0 256 256"
                                width="20px" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                </path>
                            </svg>
                        </a>
                        <a class="flex items-center justify-between p-4 border-b border-gray-100" href="#">
                            <p class="text-base font-medium text-[var(--text-primary)]">Language</p>
                            <div class="flex items-center gap-2">
                                <p class="text-base text-[var(--text-secondary)]">English</p>
                                <svg class="text-gray-400" fill="currentColor" height="20px" viewBox="0 0 256 256"
                                    width="20px" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                    </path>
                                </svg>
                            </div>
                        </a>
                        <a class="flex items-center justify-between p-4 border-b border-gray-100" href="#">
                            <p class="text-base font-medium text-[var(--text-primary)]">Help</p>
                            <svg class="text-gray-400" fill="currentColor" height="20px" viewBox="0 0 256 256"
                                width="20px" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                </path>
                            </svg>
                        </a>
                        <a class="flex items-center justify-between p-4" href="#">
                            <p class="text-base font-medium text-[var(--text-primary)]">About</p>
                            <svg class="text-gray-400" fill="currentColor" height="20px" viewBox="0 0 256 256"
                                width="20px" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M221.66,133.66l-72,72a8,8,0,0,1-11.32-11.32L196.69,136H40a8,8,0,0,1,0-16H196.69L138.34,61.66a8,8,0,0,1,11.32-11.32l72,72A8,8,0,0,1,221.66,133.66Z">
                                </path>
                            </svg>
                        </a>
                    </div>
                </section>
            </main>
        </div>
        <footer class="bg-white sticky bottom-0 z-10 border-t border-gray-200">
            <nav class="flex justify-around max-w-md mx-auto py-2">
                <a class="flex flex-col items-center justify-center gap-1 text-[var(--text-secondary)] w-1/3" href="#">
                    <svg class="h-6 w-6" fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M228.92,49.69a8,8,0,0,0-6.86-1.45L160.93,63.52,99.58,32.84a8,8,0,0,0-5.52-.6l-64,16A8,8,0,0,0,24,56V200a8,8,0,0,0,9.94,7.76l61.13-15.28,61.35,30.68A8.15,8.15,0,0,0,160,224a8,8,0,0,0,1.94-.24l64-16A8,8,0,0,0,232,200V56A8,8,0,0,0,228.92,49.69ZM104,52.94l48,24V203.06l-48-24ZM40,62.25l48-12v127.5l-48,12Zm176,131.5-48,12V78.25l48-12Z">
                        </path>
                    </svg>
                    <p class="text-xs font-medium">Map</p>
                </a>
                <a class="flex flex-col items-center justify-center gap-1 text-[var(--text-secondary)] w-1/3" href="#">
                    <svg class="h-6 w-6" fill="currentColor" height="24px" viewBox="0 0 256 256" width="24px"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M117.25,157.92a60,60,0,1,0-66.5,0A95.83,95.83,0,0,0,3.53,195.63a8,8,0,1,0,13.4,8.74,80,80,0,0,1,134.14,0,8,8,0,0,0,13.4-8.74A95.83,95.83,0,0,0,117.25,157.92ZM40,108a44,44,0,1,1,44,44A44.05,44.05,0,0,1,40,108Zm210.14,98.7a8,8,0,0,1-11.07-2.33A79.83,79.83,0,0,0,172,168a8,8,0,0,1,0-16,44,44,0,1,0-16.34-84.87,8,8,0,1,1-5.94-14.85,60,60,0,0,1,55.53,105.64,95.83,95.83,0,0,1,47.22,37.71A8,8,0,0,1,250.14,206.7Z">
                        </path>
                    </svg>
                    <p class="text-xs font-medium">Friends</p>
                </a>
                <a class="flex flex-col items-center justify-center gap-1 text-[var(--primary-color)] w-1/3" href="#">
                    <div class="relative">
                        <div class="absolute -inset-2 bg-[var(--secondary-color)] rounded-full"></div>
                        <svg class="h-6 w-6 relative text-[var(--primary-color)]" fill="currentColor" height="24px"
                            viewBox="0 0 256 256" width="24px" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M216,130.16q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.6,107.6,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.29,107.29,0,0,0-26.25-10.86,8,8,0,0,0-7.06,1.48L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.6,107.6,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06ZM128,168a40,40,0,1,1,40-40A40,40,0,0,1,128,168Z">
                            </path>
                        </svg>
                    </div>
                    <p class="text-xs font-bold">Settings</p>
                </a>
            </nav>
        </footer>
    </div>

</body>

</html>