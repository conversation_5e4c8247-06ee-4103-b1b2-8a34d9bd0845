<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <title>SYK School - Reset Password</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700;900&amp;display=swap"
        rel="stylesheet" />
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #d4e7f9;
        --background-color: #f8faff;
        --text-primary: #1a202c;
        --text-secondary: #4a5568;
        --accent-color: #82b9f0;
      }
      body {
        font-family: "Inter", sans-serif;
      }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="bg-[var(--background-color)] text-[var(--text-primary)]">
    <div class="relative flex size-full min-h-screen flex-col justify-between overflow-x-hidden">
        <header class="bg-white p-4">
            <div class="flex items-center justify-between">
                <button class="text-[var(--text-primary)]">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                        </path>
                    </svg>
                </button>
                <button class="text-[var(--text-primary)]">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M140,180a12,12,0,1,1-12-12A12,12,0,0,1,140,180ZM128,72c-22.06,0-40,16.15-40,36v4a8,8,0,0,0,16,0v-4c0-11,10.77-20,24-20s24,9,24,20-10.77,20-24,20a8,8,0,0,0-8,8v8a8,8,0,0,0,16,0v-.72c18.24-3.35,32-17.9,32-35.28C168,88.15,150.06,72,128,72Zm104,56A104,104,0,1,1,128,24,104.11,104.11,0,0,1,232,128Zm-16,0a88,88,0,1,0-88,88A88.1,88.1,0,0,0,216,128Z">
                        </path>
                    </svg>
                </button>
            </div>
        </header>
        <main class="flex-grow px-4 pt-8 pb-6">
            <div class="max-w-md mx-auto">
                <h1 class="text-3xl font-bold text-[var(--text-primary)] mb-2">Reset Password</h1>
                <p class="text-[var(--text-secondary)] mb-8">Enter the email associated with your account and we'll send
                    an email with instructions to reset your password.</p>
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-[var(--text-primary)] mb-1"
                            for="email">Email</label>
                        <input
                            class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-white text-[var(--text-primary)] placeholder-[var(--text-secondary)] focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:border-transparent transition-colors duration-200"
                            id="email" placeholder="<EMAIL>" type="email" />
                    </div>
                </div>
            </div>
        </main>
        <footer class="px-4 pb-8 pt-4">
            <div class="max-w-md mx-auto">
                <button
                    class="w-full bg-[var(--primary-color)] text-white px-6 py-4 rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-[var(--primary-color)] focus:ring-opacity-50 transition-colors duration-200 ease-in-out font-bold">
                    Send Reset Link
                </button>
            </div>
        </footer>
    </div>


</body>

</html>