<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta content="width=device-width, initial-scale=1.0" name="viewport" />
    <link href="https://fonts.googleapis.com" rel="preconnect" />
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect" />
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@400;500;700;800&amp;display=swap"
        rel="stylesheet" />
    <title>SYK School - Friends</title>
    <link href="data:image/x-icon;base64," rel="icon" type="image/x-icon" />
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
        --primary-color: #1173d4;
        --secondary-color: #e0efff;
        --background-color: #f8faff;
        --text-primary: #1a202c;
        --text-secondary: #5a677d;
        --accent-color: #1173d4;
      }
      body {
        font-family: 'Plus Jakarta Sans', sans-serif;
        background-color: var(--background-color);
        color: var(--text-primary);
      }
    </style>
    <style>
        body {
            min-height: max(884px, 100dvh);
        }
    </style>
</head>

<body class="antialiased">
    <div
        class="relative flex size-full min-h-screen flex-col justify-between group/design-root overflow-x-hidden bg-[var(--background-color)]">
        <div class="flex-grow">
            <header class="sticky top-0 z-10 bg-white shadow-sm">
                <div class="flex items-center justify-between p-4">
                    <button class="flex size-10 items-center justify-center rounded-full hover:bg-gray-100">
                        <svg class="text-[var(--text-primary)]" fill="currentColor" height="24" viewBox="0 0 256 256"
                            width="24" xmlns="http://www.w3.org/2000/svg">
                            <path
                                d="M224,128a8,8,0,0,1-8,8H59.31l58.35,58.34a8,8,0,0,1-11.32,11.32l-72-72a8,8,0,0,1,0-11.32l72-72a8,8,0,0,1,11.32,11.32L59.31,120H216A8,8,0,0,1,224,128Z">
                            </path>
                        </svg>
                    </button>
                    <h1 class="text-xl font-bold text-[var(--text-primary)]">Friends</h1>
                    <div class="size-10"></div>
                </div>
            </header>
            <main class="p-4">
                <section class="mb-8">
                    <h2 class="mb-3 text-lg font-bold text-[var(--text-primary)]">My Friends</h2>
                    <div class="space-y-2">
                        <div
                            class="flex cursor-pointer items-center gap-4 rounded-xl bg-white p-3 shadow-sm transition-all hover:shadow-md">
                            <img alt="Ethan Carter" class="size-14 rounded-full object-cover"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuCFtOsdjoKdtxwbrhKT8t-OvQY9RM0Qh78vv5PNbL6vCNefYIzHjS8sOvb5qOL41Himv2K2TADM1l7AiCk1bDp05tnSxGAIoOSa9UF3qhK8zvTsNOpSxVbsPc9PIyhcje1Gyl7XLlBX5GyZ4x3jynaEBnCiWkkkrfVgt48mmVW1bTdrf0ezHJk1uje9XP_FZE52LBQNu7pYTOYp2r10d7KL2XIJeoumxpEcLLazzX0B0rndxonxFcnX1MMrPiF_aZMkSmnzRecpCLQ3" />
                            <div class="flex-1">
                                <p class="font-semibold text-[var(--text-primary)]">Ethan Carter</p>
                                <p class="text-sm text-[var(--text-secondary)]">Class of 2024</p>
                            </div>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </div>
                        <div
                            class="flex cursor-pointer items-center gap-4 rounded-xl bg-white p-3 shadow-sm transition-all hover:shadow-md">
                            <img alt="Olivia Bennett" class="size-14 rounded-full object-cover"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuAVAc7bh8wx19icexlA33vg09X5iZy29obpRYdiNfYdPxA9u437XkXTf_chZidDNdyeo14sayqfEWDFyUOapQ50xB5XN2uP57_YvCHLFXdQweptWV0M9PKkHqB2ly37Yt1UT53vKDfx3Lu2rLQB864piR-voVd2CV9-6-o0xmbyirqzCiPLqUAMHGwq-BB9adIV2VpCDBJKBCRr9AMp8mI5okc54JcnaIsAHm5AQlUsJBnZwajX40d1L9MLBsa1G8CN7fSRty1cflF0" />
                            <div class="flex-1">
                                <p class="font-semibold text-[var(--text-primary)]">Olivia Bennett</p>
                                <p class="text-sm text-[var(--text-secondary)]">Class of 2025</p>
                            </div>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </div>
                        <div
                            class="flex cursor-pointer items-center gap-4 rounded-xl bg-white p-3 shadow-sm transition-all hover:shadow-md">
                            <img alt="Noah Thompson" class="size-14 rounded-full object-cover"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuAg3iZFtBrmOJhmnaFOUlVHrzs4OZF78OKyVrRXIrEMz0oh8rAkboYZ6mFG8TxRX2Jz4IGSJAF8zYwxOiZrPmrM0CwCfAFPBb_kKKwa8aqLyKd2pd7F4K_ThVxW-RhworPPWyG6_iYaJ8d1J5sJSSn6BVqcrvx0wAVhBCvvF4X4PKdzbwwm9xu3UOiUcYMIwmjctMd1oidpmyQsBG6m5lmBi9IeosE-5vcdNknwIolnAzzJc_tZJMaY5WFsHwgUk_ZSGv5wgiDD9wg-" />
                            <div class="flex-1">
                                <p class="font-semibold text-[var(--text-primary)]">Noah Thompson</p>
                                <p class="text-sm text-[var(--text-secondary)]">Class of 2024</p>
                            </div>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </div>
                        <div
                            class="flex cursor-pointer items-center gap-4 rounded-xl bg-white p-3 shadow-sm transition-all hover:shadow-md">
                            <img alt="Ava Harper" class="size-14 rounded-full object-cover"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuCNm5gOsxX474A5hIwoELTbtRUxrvDuEoovXs4hzrpj93M50um4Zhvn_gSwNdv7XAaEr5nFqa0T50jLmCejjFVGroUkH9ol0JPsTc6b6bITmhdnogSZ5vtlJW18_INoORGIlpe8YFvGr6q19MRBGbBAeNzqE7MoqSnXBBcydTI0BJml7U5MCsKeqck6UYgohgZY76CFkqthgYbK0frUqB01eTtlATOGbe2eAMCHzWarTYf35g5hpNS_XqkWPYBr7i_GFyNm5Vqszjfj" />
                            <div class="flex-1">
                                <p class="font-semibold text-[var(--text-primary)]">Ava Harper</p>
                                <p class="text-sm text-[var(--text-secondary)]">Class of 2025</p>
                            </div>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </div>
                        <div
                            class="flex cursor-pointer items-center gap-4 rounded-xl bg-white p-3 shadow-sm transition-all hover:shadow-md">
                            <img alt="Liam Foster" class="size-14 rounded-full object-cover"
                                src="https://lh3.googleusercontent.com/aida-public/AB6AXuB3_Q5iDjztd7bpmuikQ5IoA6hSnTPN4im-5hardUmvV724H14AfodPwuYVCaNXbUCzkpaQErWR67-b7oa4pTFfCb5kyab9q_KxTv20iyJucZ01H0ecwwy7QSOx5YwulByc6PUmSBAb17TkuH5bA4rq5oSFvw2TlBvVvjaPhafFZ8tFYKUOcL9lv5h7Dn8ghrdjkcP1XwqIAi_1GrOdqi8utYr3YAdrFHr7RqM2s-NJPb2Qu7aUIiVnamrG2D0pbd36ZOVuWtu_RWxb" />
                            <div class="flex-1">
                                <p class="font-semibold text-[var(--text-primary)]">Liam Foster</p>
                                <p class="text-sm text-[var(--text-secondary)]">Class of 2024</p>
                            </div>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </div>
                    </div>
                </section>
                <section>
                    <h2 class="mb-3 text-lg font-bold text-[var(--text-primary)]">Add Friends</h2>
                    <div class="space-y-2">
                        <a class="flex items-center gap-4 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-md"
                            href="#">
                            <div
                                class="flex size-10 items-center justify-center rounded-full bg-[var(--secondary-color)]">
                                <svg class="text-[var(--primary-color)]" fill="currentColor" height="24"
                                    viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M229.66,218.34l-50.07-50.06a88.11,88.11,0,1,0-11.31,11.31l50.06,50.07a8,8,0,0,0,11.32-11.32ZM40,112a72,72,0,1,1,72,72A72.08,72.08,0,0,1,40,112Z">
                                    </path>
                                </svg>
                            </div>
                            <p class="flex-1 font-medium text-[var(--text-primary)]">Search by Name or Email</p>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </a>
                        <a class="flex items-center gap-4 rounded-xl bg-white p-4 shadow-sm transition-all hover:shadow-md"
                            href="#">
                            <div
                                class="flex size-10 items-center justify-center rounded-full bg-[var(--secondary-color)]">
                                <svg class="text-[var(--primary-color)]" fill="currentColor" height="24"
                                    viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M137.54,186.36a8,8,0,0,1,0,11.31l-9.94,10A56,56,0,0,1,48.38,128.4L72.5,104.28A56,56,0,0,1,149.31,102a8,8,0,1,1-10.64,12,40,40,0,0,0-54.85,1.63L59.7,139.72a40,40,0,0,0,56.58,56.58l9.94-9.94A8,8,0,0,1,137.54,186.36Zm70.08-138a56.08,56.08,0,0,0-79.22,0l-9.94,9.95a8,8,0,0,0,11.32,11.31l9.94-9.94a40,40,0,0,1,56.58,56.58L172.18,140.4A40,40,0,0,1,117.33,142,8,8,0,1,0,106.69,154a56,56,0,0,0,76.81-2.26l24.12-24.12A56.08,56.08,0,0,0,207.62,48.38Z">
                                    </path>
                                </svg>
                            </div>
                            <p class="flex-1 font-medium text-[var(--text-primary)]">Invite via Link</p>
                            <svg class="text-[var(--text-secondary)]" fill="currentColor" height="24"
                                viewBox="0 0 256 256" width="24" xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M181.66,133.66l-80,80a8,8,0,0,1-11.32-11.32L164.69,128,90.34,53.66a8,8,0,0,1,11.32-11.32l80,80A8,8,0,0,1,181.66,133.66Z">
                                </path>
                            </svg>
                        </a>
                    </div>
                </section>
            </main>
        </div>
        <footer class="sticky bottom-0 bg-white shadow-[0_-1px_3px_rgba(0,0,0,0.05)]">
            <nav class="flex justify-around p-2">
                <a class="flex flex-col items-center gap-1 p-2 text-[var(--text-secondary)]" href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M228.92,49.69a8,8,0,0,0-6.86-1.45L160.93,63.52,99.58,32.84a8,8,0,0,0-5.52-.6l-64,16A8,8,0,0,0,24,56V200a8,8,0,0,0,9.94,7.76l61.13-15.28,61.35,30.68A8.15,8.15,0,0,0,160,224a8,8,0,0,0,1.94-.24l64-16A8,8,0,0,0,232,200V56A8,8,0,0,0,228.92,49.69ZM104,52.94l48,24V203.06l-48-24ZM40,62.25l48-12v127.5l-48,12Zm176,131.5-48,12V78.25l48-12Z">
                        </path>
                    </svg>
                    <span class="text-xs font-medium">Map</span>
                </a>
                <a class="flex flex-col items-center gap-1 rounded-xl bg-[var(--secondary-color)] p-2 px-4 text-[var(--primary-color)]"
                    href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M164.47,195.63a8,8,0,0,1-6.7,12.37H10.23a8,8,0,0,1-6.7-12.37,95.83,95.83,0,0,1,47.22-37.71,60,60,0,1,1,66.5,0A95.83,95.83,0,0,1,164.47,195.63Zm87.91-.15a95.87,95.87,0,0,0-47.13-37.56A60,60,0,0,0,144.7,54.59a4,4,0,0,0-1.33,6A75.83,75.83,0,0,1,147,150.53a4,4,0,0,0,1.07,5.53,112.32,112.32,0,0,1,29.85,30.83,23.92,23.92,0,0,1,3.65,16.47,4,4,0,0,0,3.95,4.64h60.3a8,8,0,0,0,7.73-5.93A8.22,8.22,0,0,0,252.38,195.48Z">
                        </path>
                    </svg>
                    <span class="text-xs font-bold">Friends</span>
                </a>
                <a class="flex flex-col items-center gap-1 p-2 text-[var(--text-secondary)]" href="#">
                    <svg fill="currentColor" height="24" viewBox="0 0 256 256" width="24"
                        xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M128,80a48,48,0,1,0,48,48A48.05,48.05,0,0,0,128,80Zm0,80a32,32,0,1,1,32-32A32,32,0,0,1,128,160Zm88-29.84q.06-2.16,0-4.32l14.92-18.64a8,8,0,0,0,1.48-7.06,107.21,107.21,0,0,0-10.88-26.25,8,8,0,0,0-6-3.93l-23.72-2.64q-1.48-1.56-3-3L186,40.54a8,8,0,0,0-3.94-6,107.71,107.71,0,0,0-26.25-10.87,8,8,0,0,0-7.06,1.49L130.16,40Q128,40,125.84,40L107.2,25.11a8,8,0,0,0-7.06-1.48A107.6,107.6,0,0,0,73.89,34.51a8,8,0,0,0-3.93,6L67.32,64.27q-1.56,1.49-3,3L40.54,70a8,8,0,0,0-6,3.94,107.71,107.71,0,0,0-10.87,26.25,8,8,0,0,0,1.49,7.06L40,125.84Q40,128,40,130.16L25.11,148.8a8,8,0,0,0-1.48,7.06,107.21,107.21,0,0,0,10.88,26.25,8,8,0,0,0,6,3.93l23.72,2.64q1.49,1.56,3,3L70,215.46a8,8,0,0,0,3.94,6,107.71,107.71,0,0,0,26.25,10.87,8,8,0,0,0,7.06-1.49L125.84,216q2.16.06,4.32,0l18.64,14.92a8,8,0,0,0,7.06,1.48,107.21,107.21,0,0,0,26.25-10.88,8,8,0,0,0,3.93-6l2.64-23.72q1.56-1.48,3-3L215.46,186a8,8,0,0,0,6-3.94,107.71,107.71,0,0,0,10.87-26.25,8,8,0,0,0-1.49-7.06Zm-16.1-6.5a73.93,73.93,0,0,1,0,8.68,8,8,0,0,0,1.74,5.48l14.19,17.73a91.57,91.57,0,0,1-6.23,15L187,173.11a8,8,0,0,0-5.1,2.64,74.11,74.11,0,0,1-6.14,6.14,8,8,0,0,0-2.64,5.1l-2.51,22.58a91.32,91.32,0,0,1-15,6.23l-17.74-14.19a8,8,0,0,0-5-1.75h-.48a73.93,73.93,0,0,1-8.68,0,8,8,0,0,0-5.48,1.74L100.45,215.8a91.57,91.57,0,0,1-15-6.23L82.89,187a8,8,0,0,0-2.64-5.1,74.11,74.11,0,0,1-6.14-6.14,8,8,0,0,0-5.1-2.64L46.43,170.6a91.32,91.32,0,0,1-6.23-15l14.19-17.74a8,8,0,0,0,1.74-5.48,73.93,73.93,0,0,1,0-8.68,8,8,0,0,0-1.74-5.48L40.2,100.45a91.57,91.57,0,0,1,6.23-15L69,82.89a8,8,0,0,0,5.1-2.64,74.11,74.11,0,0,1,6.14-6.14A8,8,0,0,0,82.89,69L85.4,46.43a91.32,91.32,0,0,1,15-6.23l17.74,14.19a8,8,0,0,0,5.48,1.74,73.93,73.93,0,0,1,8.68,0,8,8,0,0,0,5.48-1.74L155.55,40.2a91.57,91.57,0,0,1,15,6.23L173.11,69a8,8,0,0,0,2.64,5.1,74.11,74.11,0,0,1,6.14,6.14,8,8,0,0,0,5.1,2.64l22.58,2.51a91.32,91.32,0,0,1,6.23,15l-14.19,17.74A8,8,0,0,0,199.87,123.66Z">
                        </path>
                    </svg>
                    <span class="text-xs font-medium">Settings</span>
                </a>
            </nav>
        </footer>
    </div>

</body>

</html>